"use client";

import React, { memo, useMemo, useCallback, Suspense, lazy } from "react";
import { ReactFlow, Background, Controls, Panel, BackgroundVariant, ConnectionMode } from "reactflow";
import "reactflow/dist/style.css";
import "./workflow-styles.css";

import { withOptimization, useOptimizedState, useDebouncedValue } from "@/components/optimized-wrapper";
import { performanceMonitor } from "@/lib/performance/performance-monitor";
import { useTheme } from "@/components/theme-provider";

// Lazy load heavy components for better performance
const NodeSelector = lazy(() => import("./node-selector"));
const WorkflowControls = lazy(() => import("./workflow-controls"));
const WorkflowTemplates = lazy(() => import("./workflow-templates"));
const ExecutionPanel = lazy(() => import("./execution-panel"));

// Loading components
const LoadingSkeleton = memo(function LoadingSkeleton() {
  return (
    <div className="h-8 w-32 bg-muted animate-pulse rounded" />
  );
});

const WorkflowLoading = memo(function WorkflowLoading() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  );
});

// Optimized ReactFlow component
const OptimizedReactFlow = memo(function OptimizedReactFlow({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onSelectionChange,
  nodeTypes,
  isDarkTheme,
}: any) {
  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={onConnect}
      onSelectionChange={onSelectionChange}
      nodeTypes={nodeTypes}
      fitView
      proOptions={{
        hideAttribution: true,
        account: 'paid-pro'
      }}
      nodesDraggable
      elementsSelectable
      selectNodesOnDrag
      multiSelectionKeyCode="Control"
      className={isDarkTheme ? "bg-stone-900" : "bg-stone-50"}
      style={{
        height: '100%',
        width: '100%',
        position: 'absolute',
        transition: 'background-color 0.3s ease'
      }}
      snapToGrid={true}
      snapGrid={[16, 16]}
      connectionMode={ConnectionMode.Loose}
      deleteKeyCode="Delete"
      selectionKeyCode="Shift"
      zoomActivationKeyCode="Meta"
      panActivationKeyCode="Space"
      zoomOnPinch={true}
      onlyRenderVisibleElements={true} // Enable viewport optimization
      maxZoom={2}
      minZoom={0.1}
    >
      <Background
        color={isDarkTheme ? "rgba(255, 255, 255, 0.15)" : "rgba(0, 0, 0, 0.15)"}
        gap={16}
        size={1}
        variant={BackgroundVariant.Dots}
      />
      <Controls 
        className="bg-background border shadow-sm rounded-md transition-colors" 
        showInteractive={false} 
      />
    </ReactFlow>
  );
});

// Optimized Panel components
const OptimizedTopPanel = memo(function OptimizedTopPanel({
  onSelectTemplate,
  onAddNode,
}: {
  onSelectTemplate: (template: any) => void;
  onAddNode: (nodeType: string) => void;
}) {
  return (
    <Panel position="top-left" className="bg-background/50 backdrop-blur-sm p-2 rounded-md shadow-sm transition-colors pointer-events-auto">
      <div className="flex gap-2 pointer-events-auto">
        <Suspense fallback={<LoadingSkeleton />}>
          <WorkflowTemplates onSelectTemplate={onSelectTemplate} />
        </Suspense>
        <Suspense fallback={<LoadingSkeleton />}>
          <NodeSelector onAddNode={onAddNode} />
        </Suspense>
      </div>
    </Panel>
  );
});

const OptimizedControlsPanel = memo(function OptimizedControlsPanel({
  nodes,
  edges,
  onSave,
  onLoad,
  onClear,
  onDeleteSelected,
  onUpdateDetails,
  hasSelectedElements,
  initialName,
  initialDescription,
  workflowId,
}: any) {
  return (
    <Suspense fallback={<LoadingSkeleton />}>
      <WorkflowControls
        nodes={nodes}
        edges={edges}
        onSave={onSave}
        onLoad={onLoad}
        onClear={onClear}
        onDeleteSelected={onDeleteSelected}
        onUpdateDetails={onUpdateDetails}
        hasSelectedElements={hasSelectedElements}
        initialName={initialName}
        initialDescription={initialDescription}
        workflowId={workflowId}
      />
    </Suspense>
  );
});

// Main optimized workflow container
interface OptimizedWorkflowContainerProps {
  initialWorkflow?: any;
  nodes: any[];
  edges: any[];
  nodeTypes: any;
  onNodesChange: (changes: any) => void;
  onEdgesChange: (changes: any) => void;
  onConnect: (connection: any) => void;
  onSelectionChange: (selection: any) => void;
  onAddNode: (nodeType: string) => void;
  onSelectTemplate: (template: any) => void;
  onSaveWorkflow: () => void;
  onLoadWorkflow: () => void;
  onClearWorkflow: () => void;
  onDeleteSelected: () => void;
  onUpdateDetails: (details: any) => void;
  selectedElements: any;
  workflowId?: string;
  workflowName?: string;
  workflowDescription?: string;
}

const OptimizedWorkflowContainer = memo(function OptimizedWorkflowContainer({
  initialWorkflow,
  nodes,
  edges,
  nodeTypes,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onSelectionChange,
  onAddNode,
  onSelectTemplate,
  onSaveWorkflow,
  onLoadWorkflow,
  onClearWorkflow,
  onDeleteSelected,
  onUpdateDetails,
  selectedElements,
  workflowId,
  workflowName,
  workflowDescription,
}: OptimizedWorkflowContainerProps) {
  const { theme } = useTheme();
  const isDarkTheme = theme === 'dark';

  // Memoize expensive calculations
  const hasSelectedElements = useMemo(() => {
    return selectedElements.nodes.length > 0 || selectedElements.edges.length > 0;
  }, [selectedElements]);

  // Debounce frequent operations
  const debouncedNodes = useDebouncedValue(nodes, 100);
  const debouncedEdges = useDebouncedValue(edges, 100);

  // Performance monitoring
  React.useEffect(() => {
    performanceMonitor.recordMetric('workflow.render', performance.now(), 'timing');
  });

  return (
    <div className="workflow-container" style={{ width: '100%', height: '100%', position: 'absolute' }}>
      <OptimizedReactFlow
        nodes={debouncedNodes}
        edges={debouncedEdges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onSelectionChange={onSelectionChange}
        nodeTypes={nodeTypes}
        isDarkTheme={isDarkTheme}
      />
      
      <OptimizedTopPanel
        onSelectTemplate={onSelectTemplate}
        onAddNode={onAddNode}
      />
      
      <OptimizedControlsPanel
        nodes={nodes}
        edges={edges}
        onSave={onSaveWorkflow}
        onLoad={onLoadWorkflow}
        onClear={onClearWorkflow}
        onDeleteSelected={onDeleteSelected}
        onUpdateDetails={onUpdateDetails}
        hasSelectedElements={hasSelectedElements}
        initialName={workflowName}
        initialDescription={workflowDescription}
        workflowId={workflowId}
      />

      {/* Execution Panel */}
      {workflowId && (
        <Suspense fallback={null}>
          <ExecutionPanel
            workflowId={workflowId}
            nodes={nodes}
            edges={edges}
            context={{
              userId: initialWorkflow?.userId || 'temp-user',
              workflowId: workflowId,
              sessionId: `session-${Date.now()}`,
            }}
          />
        </Suspense>
      )}
    </div>
  );
});

// Export with performance monitoring
export default withOptimization(OptimizedWorkflowContainer, {
  displayName: 'OptimizedWorkflowContainer',
  enableProfiling: true,
  shouldUpdate: (prevProps, nextProps) => {
    // Custom comparison for better performance
    return (
      prevProps.nodes !== nextProps.nodes ||
      prevProps.edges !== nextProps.edges ||
      prevProps.workflowId !== nextProps.workflowId ||
      prevProps.selectedElements !== nextProps.selectedElements
    );
  },
});
