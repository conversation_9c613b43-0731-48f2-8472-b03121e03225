"use client";

import React, { memo, useMemo, useCallback, Suspense } from 'react';
import { performanceMonitor } from '@/lib/performance/performance-monitor';

// Loading skeleton component
const LoadingSkeleton = memo(function LoadingSkeleton({ 
  className = "h-4 w-full bg-muted animate-pulse rounded" 
}: { 
  className?: string 
}) {
  return <div className={className} />;
});

// Error boundary component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Component error:', error, errorInfo);
    performanceMonitor.recordMetric('component.error', 1, 'counter', {
      error: error.message,
      stack: error.stack?.substring(0, 100),
    });
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      if (FallbackComponent && this.state.error) {
        return <FallbackComponent error={this.state.error} />;
      }
      return (
        <div className="p-4 border border-destructive rounded-md bg-destructive/10">
          <h3 className="text-sm font-medium text-destructive">Something went wrong</h3>
          <p className="text-xs text-muted-foreground mt-1">
            {this.state.error?.message || 'An unexpected error occurred'}
          </p>
        </div>
      );
    }

    return this.props.children;
  }
}

// Performance optimized component wrapper
export function withOptimization<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: {
    displayName?: string;
    shouldUpdate?: (prevProps: P, nextProps: P) => boolean;
    errorFallback?: React.ComponentType<{ error: Error }>;
    loadingFallback?: React.ComponentType;
    enableProfiling?: boolean;
  } = {}
) {
  const {
    displayName = WrappedComponent.displayName || WrappedComponent.name || 'Component',
    shouldUpdate,
    errorFallback,
    loadingFallback = LoadingSkeleton,
    enableProfiling = process.env.NODE_ENV === 'development',
  } = options;

  const OptimizedComponent = memo(
    function OptimizedComponent(props: P) {
      const startTime = useMemo(() => performance.now(), []);

      // Record render performance
      React.useEffect(() => {
        if (enableProfiling) {
          const endTime = performance.now();
          performanceMonitor.recordComponentRender(displayName, endTime - startTime);
        }
      });

      return (
        <ErrorBoundary fallback={errorFallback}>
          <Suspense fallback={loadingFallback ? React.createElement(loadingFallback) : <LoadingSkeleton />}>
            <WrappedComponent {...props} />
          </Suspense>
        </ErrorBoundary>
      );
    },
    shouldUpdate
  );

  OptimizedComponent.displayName = `Optimized(${displayName})`;
  return OptimizedComponent;
}

// Hook for optimized state management
export function useOptimizedState<T>(
  initialState: T | (() => T),
  equalityFn?: (prev: T, next: T) => boolean
) {
  const [state, setState] = React.useState(initialState);

  const optimizedSetState = useCallback(
    (newState: T | ((prev: T) => T)) => {
      setState(prevState => {
        const nextState = typeof newState === 'function' 
          ? (newState as (prev: T) => T)(prevState)
          : newState;

        // Use custom equality function if provided
        if (equalityFn && equalityFn(prevState, nextState)) {
          return prevState; // Prevent unnecessary re-render
        }

        // Default shallow comparison for objects
        if (typeof prevState === 'object' && typeof nextState === 'object') {
          if (JSON.stringify(prevState) === JSON.stringify(nextState)) {
            return prevState;
          }
        }

        return nextState;
      });
    },
    [equalityFn]
  );

  return [state, optimizedSetState] as const;
}

// Hook for debounced values
export function useDebouncedValue<T>(value: T, delay: number = 300) {
  const [debouncedValue, setDebouncedValue] = React.useState(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Hook for throttled callbacks
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number = 100
): T {
  const lastRun = React.useRef(Date.now());

  return useCallback(
    ((...args: any[]) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
}

// Hook for intersection observer (lazy loading)
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = React.useState(false);

  React.useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        ...options,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [elementRef, options]);

  return isIntersecting;
}

// Lazy loading component wrapper
export function LazyComponent<P extends object>({
  component: Component,
  fallback = <LoadingSkeleton />,
  ...props
}: {
  component: React.ComponentType<P>;
  fallback?: React.ReactNode;
} & P) {
  const ref = React.useRef<HTMLDivElement>(null);
  const isVisible = useIntersectionObserver(ref);
  const [hasLoaded, setHasLoaded] = React.useState(false);

  React.useEffect(() => {
    if (isVisible && !hasLoaded) {
      setHasLoaded(true);
    }
  }, [isVisible, hasLoaded]);

  return (
    <div ref={ref}>
      {hasLoaded ? <Component {...(props as P)} /> : fallback}
    </div>
  );
}

// Virtual list component for large datasets
export function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
}: {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
}) {
  const [scrollTop, setScrollTop] = React.useState(0);

  const visibleStart = Math.floor(scrollTop / itemHeight);
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight),
    items.length - 1
  );

  const startIndex = Math.max(0, visibleStart - overscan);
  const endIndex = Math.min(items.length - 1, visibleEnd + overscan);

  const visibleItems = items.slice(startIndex, endIndex + 1);

  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  return (
    <div
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={(e) => setScrollTop(e.currentTarget.scrollTop)}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div key={startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Performance monitoring HOC
export function withPerformanceMonitoring<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) {
  const name = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Unknown';
  
  return memo(function PerformanceMonitoredComponent(props: P) {
    const startTime = React.useRef(performance.now());
    
    React.useEffect(() => {
      const endTime = performance.now();
      performanceMonitor.recordComponentRender(name, endTime - startTime.current);
    });
    
    return <WrappedComponent {...props} />;
  });
}

// Export default optimized component
export default withOptimization;
