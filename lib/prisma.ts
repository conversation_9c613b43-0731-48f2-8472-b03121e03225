import { PrismaClient } from '@prisma/client';
import { performanceMonitor } from './performance/performance-monitor';

// PrismaClient is attached to the `global` object in development to prevent
// exhausting your database connection limit.
// Learn more: https://pris.ly/d/help/next-js-best-practices

const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Enhanced Prisma configuration for better performance
export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });

// Add performance monitoring middleware
prisma.$use(async (params, next) => {
  const startTime = Date.now();
  const result = await next(params);
  const endTime = Date.now();

  // Record query performance
  performanceMonitor.recordMetric(
    `db.${params.model}.${params.action}`,
    endTime - startTime,
    'timing',
    {
      model: params.model || 'unknown',
      action: params.action,
    }
  );

  // Log slow queries in development
  if (process.env.NODE_ENV === 'development' && (endTime - startTime) > 1000) {
    console.warn(`Slow query detected: ${params.model}.${params.action} took ${endTime - startTime}ms`);
  }

  return result;
});

// Connection management
prisma.$on('beforeExit', async () => {
  console.log('Disconnecting from database...');
  await prisma.$disconnect();
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;
