/**
 * Performance monitoring and optimization utilities
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'timing' | 'counter' | 'gauge';
  tags?: Record<string, string>;
}

interface ComponentMetric {
  componentName: string;
  renderTime: number;
  renderCount: number;
  lastRender: number;
  averageRenderTime: number;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private componentMetrics: Map<string, ComponentMetric> = new Map();
  private observers: PerformanceObserver[] = [];
  private isEnabled: boolean = process.env.NODE_ENV === 'development';

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  constructor() {
    if (typeof window !== 'undefined' && this.isEnabled) {
      this.initializeObservers();
    }
  }

  private initializeObservers() {
    // Observe navigation timing
    if ('PerformanceObserver' in window) {
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('navigation', entry.duration, 'timing', {
            type: entry.entryType,
            name: entry.name,
          });
        }
      });
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);

      // Observe resource loading
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('resource', entry.duration, 'timing', {
            type: entry.entryType,
            name: entry.name,
          });
        }
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);

      // Observe largest contentful paint
      const lcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('lcp', entry.startTime, 'timing');
        }
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);

      // Observe first input delay
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('fid', (entry as any).processingStart - entry.startTime, 'timing');
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.push(fidObserver);
    }
  }

  recordMetric(name: string, value: number, type: 'timing' | 'counter' | 'gauge', tags?: Record<string, string>) {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type,
      tags,
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const metrics = this.metrics.get(name)!;
    metrics.push(metric);

    // Keep only last 100 metrics per name
    if (metrics.length > 100) {
      metrics.shift();
    }

    // Log slow operations
    if (type === 'timing' && value > 1000) {
      console.warn(`Slow operation detected: ${name} took ${value}ms`, tags);
    }
  }

  recordComponentRender(componentName: string, renderTime: number) {
    if (!this.isEnabled) return;

    const existing = this.componentMetrics.get(componentName);
    if (existing) {
      existing.renderCount++;
      existing.lastRender = Date.now();
      existing.averageRenderTime = (existing.averageRenderTime * (existing.renderCount - 1) + renderTime) / existing.renderCount;
    } else {
      this.componentMetrics.set(componentName, {
        componentName,
        renderTime,
        renderCount: 1,
        lastRender: Date.now(),
        averageRenderTime: renderTime,
      });
    }

    // Log slow renders
    if (renderTime > 16) { // 16ms = 60fps threshold
      console.warn(`Slow render detected: ${componentName} took ${renderTime}ms`);
    }
  }

  getMetrics(name?: string): PerformanceMetric[] {
    if (name) {
      return this.metrics.get(name) || [];
    }
    
    const allMetrics: PerformanceMetric[] = [];
    for (const metrics of this.metrics.values()) {
      allMetrics.push(...metrics);
    }
    return allMetrics;
  }

  getComponentMetrics(): ComponentMetric[] {
    return Array.from(this.componentMetrics.values());
  }

  getSlowComponents(threshold: number = 16): ComponentMetric[] {
    return this.getComponentMetrics().filter(metric => metric.averageRenderTime > threshold);
  }

  getAverageMetric(name: string): number {
    const metrics = this.metrics.get(name);
    if (!metrics || metrics.length === 0) return 0;
    
    return metrics.reduce((sum, metric) => sum + metric.value, 0) / metrics.length;
  }

  getWebVitals(): {
    lcp: number;
    fid: number;
    cls: number;
  } {
    return {
      lcp: this.getAverageMetric('lcp'),
      fid: this.getAverageMetric('fid'),
      cls: this.getAverageMetric('cls'),
    };
  }

  clear() {
    this.metrics.clear();
    this.componentMetrics.clear();
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.clear();
  }

  // React performance hook
  measureRender<T extends (...args: any[]) => any>(
    componentName: string,
    renderFunction: T
  ): T {
    if (!this.isEnabled) return renderFunction;

    return ((...args: any[]) => {
      const startTime = performance.now();
      const result = renderFunction(...args);
      const endTime = performance.now();
      
      this.recordComponentRender(componentName, endTime - startTime);
      
      return result;
    }) as T;
  }

  // API call performance wrapper
  measureApiCall<T>(name: string, apiCall: () => Promise<T>): Promise<T> {
    if (!this.isEnabled) return apiCall();

    const startTime = performance.now();
    return apiCall().then(
      (result) => {
        const endTime = performance.now();
        this.recordMetric(`api.${name}`, endTime - startTime, 'timing');
        return result;
      },
      (error) => {
        const endTime = performance.now();
        this.recordMetric(`api.${name}`, endTime - startTime, 'timing', { error: 'true' });
        throw error;
      }
    );
  }

  // Database query performance wrapper
  measureDbQuery<T>(queryName: string, query: () => Promise<T>): Promise<T> {
    if (!this.isEnabled) return query();

    const startTime = performance.now();
    return query().then(
      (result) => {
        const endTime = performance.now();
        this.recordMetric(`db.${queryName}`, endTime - startTime, 'timing');
        return result;
      },
      (error) => {
        const endTime = performance.now();
        this.recordMetric(`db.${queryName}`, endTime - startTime, 'timing', { error: 'true' });
        throw error;
      }
    );
  }

  // Generate performance report
  generateReport(): {
    summary: {
      totalMetrics: number;
      averageApiTime: number;
      averageDbTime: number;
      slowComponents: number;
      webVitals: ReturnType<PerformanceMonitor['getWebVitals']>;
    };
    slowOperations: PerformanceMetric[];
    componentPerformance: ComponentMetric[];
  } {
    const allMetrics = this.getMetrics();
    const apiMetrics = allMetrics.filter(m => m.name.startsWith('api.'));
    const dbMetrics = allMetrics.filter(m => m.name.startsWith('db.'));
    const slowComponents = this.getSlowComponents();
    const slowOperations = allMetrics.filter(m => m.value > 1000);

    return {
      summary: {
        totalMetrics: allMetrics.length,
        averageApiTime: apiMetrics.reduce((sum, m) => sum + m.value, 0) / (apiMetrics.length || 1),
        averageDbTime: dbMetrics.reduce((sum, m) => sum + m.value, 0) / (dbMetrics.length || 1),
        slowComponents: slowComponents.length,
        webVitals: this.getWebVitals(),
      },
      slowOperations,
      componentPerformance: this.getComponentMetrics(),
    };
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// React hook for component performance monitoring
export function usePerformanceMonitor(componentName: string) {
  const startTime = performance.now();
  
  return {
    recordRender: () => {
      const endTime = performance.now();
      performanceMonitor.recordComponentRender(componentName, endTime - startTime);
    },
    measureRender: performanceMonitor.measureRender.bind(performanceMonitor, componentName),
  };
}

// HOC for automatic performance monitoring
export function withPerformanceMonitoring<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) {
  const name = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Unknown';
  
  return function PerformanceMonitoredComponent(props: P) {
    const { recordRender } = usePerformanceMonitor(name);
    
    React.useEffect(() => {
      recordRender();
    });
    
    return React.createElement(WrappedComponent, props);
  };
}
