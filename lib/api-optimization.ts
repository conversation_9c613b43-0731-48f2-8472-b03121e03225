/**
 * API optimization utilities for better performance
 */

import { NextRequest, NextResponse } from 'next/server';
import { performanceMonitor } from './performance/performance-monitor';

// Response compression utility
function compressResponse(data: any): string {
  if (typeof data === 'string') {
    return data;
  }
  return JSON.stringify(data);
}

// Cache headers utility
function getCacheHeaders(maxAge: number = 300): Record<string, string> {
  return {
    'Cache-Control': `public, max-age=${maxAge}, s-maxage=${maxAge}, stale-while-revalidate=86400`,
    'Vary': 'Accept-Encoding',
  };
}

// Rate limiting utility
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(identifier: string, limit: number = 100, windowMs: number = 60000): boolean {
  const now = Date.now();
  const record = rateLimitMap.get(identifier);

  if (!record || now > record.resetTime) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= limit) {
    return false;
  }

  record.count++;
  return true;
}

// API route wrapper with performance optimizations
export function withApiOptimization<T = any>(
  handler: (req: NextRequest) => Promise<NextResponse<T>>,
  options: {
    cache?: number; // Cache duration in seconds
    rateLimit?: { limit: number; windowMs: number };
    compression?: boolean;
    monitoring?: boolean;
    cors?: boolean;
  } = {}
) {
  const {
    cache = 0,
    rateLimit,
    compression = true,
    monitoring = true,
    cors = true,
  } = options;

  return async function optimizedHandler(req: NextRequest): Promise<NextResponse<T>> {
    const startTime = performance.now();
    const method = req.method;
    const pathname = new URL(req.url).pathname;
    const identifier = req.ip || 'unknown';

    try {
      // Rate limiting
      if (rateLimit) {
        if (!checkRateLimit(identifier, rateLimit.limit, rateLimit.windowMs)) {
          return NextResponse.json(
            { error: 'Too many requests' },
            { 
              status: 429,
              headers: {
                'Retry-After': Math.ceil(rateLimit.windowMs / 1000).toString(),
              }
            }
          );
        }
      }

      // Execute the handler
      const response = await handler(req);
      const endTime = performance.now();

      // Performance monitoring
      if (monitoring) {
        performanceMonitor.recordMetric(
          `api.${method}.${pathname.replace(/\//g, '.')}`,
          endTime - startTime,
          'timing',
          {
            method,
            pathname,
            status: response.status.toString(),
          }
        );
      }

      // Add optimization headers
      const headers = new Headers(response.headers);

      // Cache headers
      if (cache > 0 && method === 'GET') {
        Object.entries(getCacheHeaders(cache)).forEach(([key, value]) => {
          headers.set(key, value);
        });
      }

      // CORS headers
      if (cors) {
        headers.set('Access-Control-Allow-Origin', '*');
        headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      }

      // Compression headers
      if (compression) {
        headers.set('Content-Encoding', 'gzip');
      }

      // Performance headers
      headers.set('X-Response-Time', `${(endTime - startTime).toFixed(2)}ms`);

      return new NextResponse(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers,
      });

    } catch (error) {
      const endTime = performance.now();

      // Monitor errors
      if (monitoring) {
        performanceMonitor.recordMetric(
          `api.${method}.${pathname.replace(/\//g, '.')}.error`,
          endTime - startTime,
          'timing',
          {
            method,
            pathname,
            error: error instanceof Error ? error.message : 'Unknown error',
          }
        );
      }

      console.error(`API Error in ${method} ${pathname}:`, error);

      return NextResponse.json(
        { 
          error: 'Internal server error',
          message: process.env.NODE_ENV === 'development' 
            ? (error instanceof Error ? error.message : 'Unknown error')
            : 'Something went wrong'
        },
        { status: 500 }
      );
    }
  };
}

// Database query optimization wrapper
export async function withDbOptimization<T>(
  queryName: string,
  queryFn: () => Promise<T>,
  options: {
    cache?: boolean;
    timeout?: number;
  } = {}
): Promise<T> {
  const { cache = false, timeout = 30000 } = options;
  
  const startTime = performance.now();
  
  try {
    // Add timeout to prevent hanging queries
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Query timeout')), timeout);
    });

    const result = await Promise.race([queryFn(), timeoutPromise]);
    const endTime = performance.now();

    // Monitor query performance
    performanceMonitor.recordMetric(
      `db.${queryName}`,
      endTime - startTime,
      'timing'
    );

    return result;
  } catch (error) {
    const endTime = performance.now();
    
    performanceMonitor.recordMetric(
      `db.${queryName}.error`,
      endTime - startTime,
      'timing',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    );

    throw error;
  }
}

// Batch processing utility
export async function batchProcess<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  options: {
    batchSize?: number;
    concurrency?: number;
    delay?: number;
  } = {}
): Promise<R[]> {
  const { batchSize = 10, concurrency = 3, delay = 0 } = options;
  const results: R[] = [];

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    
    // Process batch with limited concurrency
    const batchPromises = batch.map(async (item, index) => {
      if (index >= concurrency) {
        // Wait for previous items to complete
        await new Promise(resolve => setTimeout(resolve, delay * index));
      }
      return processor(item);
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Add delay between batches
    if (delay > 0 && i + batchSize < items.length) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return results;
}

// Memory usage monitoring
export function getMemoryUsage(): {
  used: number;
  total: number;
  percentage: number;
} {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    const usage = process.memoryUsage();
    return {
      used: usage.heapUsed,
      total: usage.heapTotal,
      percentage: (usage.heapUsed / usage.heapTotal) * 100,
    };
  }
  
  return { used: 0, total: 0, percentage: 0 };
}

// Cleanup utility for long-running processes
export function createCleanupHandler(cleanupFn: () => void): void {
  if (typeof process !== 'undefined') {
    process.on('SIGINT', cleanupFn);
    process.on('SIGTERM', cleanupFn);
    process.on('uncaughtException', (error) => {
      console.error('Uncaught exception:', error);
      cleanupFn();
      process.exit(1);
    });
    process.on('unhandledRejection', (reason) => {
      console.error('Unhandled rejection:', reason);
      cleanupFn();
      process.exit(1);
    });
  }
}

// Response streaming utility for large datasets
export function createStreamResponse<T>(
  data: T[],
  transform: (item: T) => any = (item) => item
): ReadableStream {
  let index = 0;

  return new ReadableStream({
    start(controller) {
      controller.enqueue('{"data":[');
    },
    
    pull(controller) {
      if (index < data.length) {
        const item = transform(data[index]);
        const chunk = index === 0 
          ? JSON.stringify(item)
          : ',' + JSON.stringify(item);
        
        controller.enqueue(chunk);
        index++;
      } else {
        controller.enqueue(']}');
        controller.close();
      }
    },
  });
}

// Export performance utilities
export const performanceUtils = {
  monitor: performanceMonitor,
  withApiOptimization,
  withDbOptimization,
  batchProcess,
  getMemoryUsage,
  createCleanupHandler,
  createStreamResponse,
};
