import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { Suspense } from "react";
import { ThemeProvider } from "@/components/theme-provider";
import { LoadingProvider } from "@/context/loading-context";
import LoadingWrapper from "@/components/loading-wrapper";
import ConditionalNavbar from "@/components/conditional-navbar";
import NextAuthSessionProvider from "@/components/session-provider";
import { SettingsProvider } from "@/lib/settings/settings-context";

// Optimize font loading with better performance
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: 'swap',
  preload: true,
  fallback: ['system-ui', 'arial'],
  adjustFontFallback: false, // Disable for better performance
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: 'swap',
  preload: true,
  fallback: ['ui-monospace', 'monospace'],
  adjustFontFallback: false,
});

export const metadata: Metadata = {
  title: "WorkflowAI - Automate with Intelligence",
  description: "Build powerful automation workflows with our visual editor. Connect your favorite tools and scale your business operations effortlessly.",
  keywords: ["workflow", "automation", "AI", "productivity", "visual editor"],
  authors: [{ name: "WorkflowAI Team" }],
  creator: "WorkflowAI",
  publisher: "WorkflowAI",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXTAUTH_URL || 'http://localhost:3000'),
  openGraph: {
    title: "WorkflowAI - Automate with Intelligence",
    description: "Build powerful automation workflows with our visual editor",
    type: "website",
    locale: "en_US",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

// Loading fallback component
function LoadingFallback() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://lh3.googleusercontent.com" />
        <link rel="dns-prefetch" href="https://avatars.githubusercontent.com" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <Suspense fallback={<LoadingFallback />}>
          <NextAuthSessionProvider>
            <Suspense fallback={<LoadingFallback />}>
              <SettingsProvider>
                <Suspense fallback={<LoadingFallback />}>
                  <ThemeProvider
                    attribute="class"
                    defaultTheme="system"
                    enableSystem
                    disableTransitionOnChange
                  >
                    <Suspense fallback={<LoadingFallback />}>
                      <LoadingProvider>
                        <div className="min-h-screen flex flex-col">
                          <Suspense fallback={null}>
                            <ConditionalNavbar />
                          </Suspense>
                          <main className="flex-1 w-full">
                            {children}
                          </main>
                          <Suspense fallback={null}>
                            <LoadingWrapper />
                          </Suspense>
                        </div>
                      </LoadingProvider>
                    </Suspense>
                  </ThemeProvider>
                </Suspense>
              </SettingsProvider>
            </Suspense>
          </NextAuthSessionProvider>
        </Suspense>
      </body>
    </html>
  );
}
