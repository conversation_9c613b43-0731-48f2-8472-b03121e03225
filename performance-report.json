{"timestamp": "2025-05-30T20:12:04.208Z", "summary": {"totalMetrics": 0, "averageApiTime": 0, "averageDbTime": 0, "slowComponents": 0, "memoryUsage": {"used": 11744976, "total": 27803648, "percentage": 42.242571909988214}}, "slowOperations": [], "componentPerformance": [], "recommendations": ["Bundle size is large (>5MB). Consider code splitting and lazy loading.", "Large components detected. Consider breaking them into smaller components.", "Many heavy dependencies detected. Consider alternatives or lazy loading.", "Enable gzip compression in production", "Use CDN for static assets", "Implement proper caching strategies", "Monitor Core Web Vitals", "Use Next.js Image optimization", "Implement service worker for caching"]}