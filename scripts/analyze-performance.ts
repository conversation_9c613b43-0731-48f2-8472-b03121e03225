#!/usr/bin/env tsx

/**
 * Performance analysis script
 * Analyzes the application performance and provides optimization recommendations
 */

import { performanceMonitor } from '../lib/performance/performance-monitor';
import { getMemoryUsage } from '../lib/api-optimization';
import fs from 'fs/promises';
import path from 'path';

interface PerformanceReport {
  timestamp: string;
  summary: {
    totalMetrics: number;
    averageApiTime: number;
    averageDbTime: number;
    slowComponents: number;
    memoryUsage: ReturnType<typeof getMemoryUsage>;
  };
  slowOperations: any[];
  componentPerformance: any[];
  recommendations: string[];
}

async function analyzeBundle() {
  console.log('📦 Analyzing bundle size...');
  
  try {
    const buildDir = path.join(process.cwd(), '.next');
    const stats = await fs.stat(buildDir);
    
    if (stats.isDirectory()) {
      const files = await fs.readdir(buildDir, { recursive: true });
      const jsFiles = files.filter(file => typeof file === 'string' && file.endsWith('.js'));
      
      let totalSize = 0;
      const fileSizes: { file: string; size: number }[] = [];
      
      for (const file of jsFiles) {
        try {
          const filePath = path.join(buildDir, file);
          const stat = await fs.stat(filePath);
          totalSize += stat.size;
          fileSizes.push({ file, size: stat.size });
        } catch (error) {
          // Skip files that can't be read
        }
      }
      
      fileSizes.sort((a, b) => b.size - a.size);
      
      console.log(`Total bundle size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
      console.log('Largest files:');
      fileSizes.slice(0, 10).forEach(({ file, size }) => {
        console.log(`  ${file}: ${(size / 1024).toFixed(2)} KB`);
      });
      
      return {
        totalSize,
        fileCount: jsFiles.length,
        largestFiles: fileSizes.slice(0, 10),
      };
    }
  } catch (error) {
    console.warn('Could not analyze bundle (run npm run build first)');
    return null;
  }
}

async function analyzeDependencies() {
  console.log('📚 Analyzing dependencies...');
  
  try {
    const packageJson = JSON.parse(
      await fs.readFile(path.join(process.cwd(), 'package.json'), 'utf-8')
    );
    
    const deps = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies,
    };
    
    const heavyDeps = [
      '@radix-ui',
      'reactflow',
      'recharts',
      'framer-motion',
      'next-auth',
      'prisma',
    ];
    
    const foundHeavyDeps = Object.keys(deps).filter(dep =>
      heavyDeps.some(heavy => dep.includes(heavy))
    );
    
    console.log('Heavy dependencies found:');
    foundHeavyDeps.forEach(dep => {
      console.log(`  ${dep}: ${deps[dep]}`);
    });
    
    return {
      totalDependencies: Object.keys(deps).length,
      heavyDependencies: foundHeavyDeps,
    };
  } catch (error) {
    console.error('Error analyzing dependencies:', error);
    return null;
  }
}

async function analyzeComponents() {
  console.log('🧩 Analyzing components...');
  
  const componentsDir = path.join(process.cwd(), 'components');
  
  try {
    const files = await fs.readdir(componentsDir, { recursive: true });
    const componentFiles = files.filter(file => 
      typeof file === 'string' && (file.endsWith('.tsx') || file.endsWith('.jsx'))
    );
    
    const largeComponents: { file: string; size: number }[] = [];
    
    for (const file of componentFiles) {
      try {
        const filePath = path.join(componentsDir, file);
        const content = await fs.readFile(filePath, 'utf-8');
        const lines = content.split('\n').length;
        
        if (lines > 200) {
          largeComponents.push({ file, size: lines });
        }
      } catch (error) {
        // Skip files that can't be read
      }
    }
    
    largeComponents.sort((a, b) => b.size - a.size);
    
    console.log(`Total components: ${componentFiles.length}`);
    if (largeComponents.length > 0) {
      console.log('Large components (>200 lines):');
      largeComponents.forEach(({ file, size }) => {
        console.log(`  ${file}: ${size} lines`);
      });
    }
    
    return {
      totalComponents: componentFiles.length,
      largeComponents,
    };
  } catch (error) {
    console.error('Error analyzing components:', error);
    return null;
  }
}

function generateRecommendations(analysis: any): string[] {
  const recommendations: string[] = [];
  
  // Bundle size recommendations
  if (analysis.bundle && analysis.bundle.totalSize > 5 * 1024 * 1024) {
    recommendations.push('Bundle size is large (>5MB). Consider code splitting and lazy loading.');
  }
  
  // Component recommendations
  if (analysis.components && analysis.components.largeComponents.length > 0) {
    recommendations.push('Large components detected. Consider breaking them into smaller components.');
  }
  
  // Dependency recommendations
  if (analysis.dependencies && analysis.dependencies.heavyDependencies.length > 5) {
    recommendations.push('Many heavy dependencies detected. Consider alternatives or lazy loading.');
  }
  
  // Memory recommendations
  const memoryUsage = getMemoryUsage();
  if (memoryUsage.percentage > 80) {
    recommendations.push('High memory usage detected. Check for memory leaks.');
  }
  
  // Performance recommendations
  const report = performanceMonitor.generateReport();
  if (report.slowOperations.length > 0) {
    recommendations.push('Slow operations detected. Optimize database queries and API calls.');
  }
  
  if (report.summary.slowComponents > 0) {
    recommendations.push('Slow components detected. Add React.memo and optimize renders.');
  }
  
  // General recommendations
  recommendations.push(
    'Enable gzip compression in production',
    'Use CDN for static assets',
    'Implement proper caching strategies',
    'Monitor Core Web Vitals',
    'Use Next.js Image optimization',
    'Implement service worker for caching'
  );
  
  return recommendations;
}

async function main() {
  console.log('🚀 Starting performance analysis...\n');
  
  const analysis = {
    bundle: await analyzeBundle(),
    dependencies: await analyzeDependencies(),
    components: await analyzeComponents(),
    memory: getMemoryUsage(),
    performance: performanceMonitor.generateReport(),
  };
  
  const recommendations = generateRecommendations(analysis);
  
  const report: PerformanceReport = {
    timestamp: new Date().toISOString(),
    summary: {
      totalMetrics: analysis.performance.summary.totalMetrics,
      averageApiTime: analysis.performance.summary.averageApiTime,
      averageDbTime: analysis.performance.summary.averageDbTime,
      slowComponents: analysis.performance.summary.slowComponents,
      memoryUsage: analysis.memory,
    },
    slowOperations: analysis.performance.slowOperations,
    componentPerformance: analysis.performance.componentPerformance,
    recommendations,
  };
  
  // Save report
  const reportPath = path.join(process.cwd(), 'performance-report.json');
  await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
  
  console.log('\n📊 Performance Analysis Complete!');
  console.log(`Report saved to: ${reportPath}`);
  
  console.log('\n🎯 Key Recommendations:');
  recommendations.slice(0, 5).forEach((rec, index) => {
    console.log(`${index + 1}. ${rec}`);
  });
  
  console.log('\n💾 Memory Usage:');
  console.log(`Used: ${(analysis.memory.used / 1024 / 1024).toFixed(2)} MB`);
  console.log(`Total: ${(analysis.memory.total / 1024 / 1024).toFixed(2)} MB`);
  console.log(`Percentage: ${analysis.memory.percentage.toFixed(2)}%`);
  
  if (analysis.performance.slowOperations.length > 0) {
    console.log('\n⚠️  Slow Operations Detected:');
    analysis.performance.slowOperations.slice(0, 5).forEach(op => {
      console.log(`  ${op.name}: ${op.value.toFixed(2)}ms`);
    });
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { main as analyzePerformance };
