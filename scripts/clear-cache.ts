#!/usr/bin/env tsx

/**
 * Cache clearing script
 * Clears various caches to improve performance
 */

import fs from 'fs/promises';
import path from 'path';

async function clearNextCache() {
  console.log('🗑️  Clearing Next.js cache...');
  
  const nextCacheDir = path.join(process.cwd(), '.next/cache');
  
  try {
    await fs.rm(nextCacheDir, { recursive: true, force: true });
    console.log('✅ Next.js cache cleared');
  } catch (error) {
    console.log('ℹ️  Next.js cache directory not found or already empty');
  }
}

async function clearNodeModulesCache() {
  console.log('🗑️  Clearing node_modules cache...');
  
  const nodeModulesDir = path.join(process.cwd(), 'node_modules/.cache');
  
  try {
    await fs.rm(nodeModulesDir, { recursive: true, force: true });
    console.log('✅ node_modules cache cleared');
  } catch (error) {
    console.log('ℹ️  node_modules cache directory not found or already empty');
  }
}

async function clearTurboCache() {
  console.log('🗑️  Clearing Turbo cache...');
  
  const turboCacheDir = path.join(process.cwd(), 'node_modules/.cache/turbo');
  
  try {
    await fs.rm(turboCacheDir, { recursive: true, force: true });
    console.log('✅ Turbo cache cleared');
  } catch (error) {
    console.log('ℹ️  Turbo cache directory not found or already empty');
  }
}

async function clearBuildCache() {
  console.log('🗑️  Clearing build cache...');
  
  const buildDirs = [
    path.join(process.cwd(), '.next'),
    path.join(process.cwd(), 'dist'),
    path.join(process.cwd(), 'out'),
  ];
  
  for (const dir of buildDirs) {
    try {
      await fs.rm(dir, { recursive: true, force: true });
      console.log(`✅ Cleared ${path.basename(dir)} directory`);
    } catch (error) {
      console.log(`ℹ️  ${path.basename(dir)} directory not found or already empty`);
    }
  }
}

async function clearTempFiles() {
  console.log('🗑️  Clearing temporary files...');
  
  const tempPatterns = [
    '**/*.tmp',
    '**/*.temp',
    '**/tsconfig.tsbuildinfo',
    '**/.DS_Store',
    '**/Thumbs.db',
  ];
  
  // Note: In a real implementation, you'd use a glob library
  // For now, just clear known temp files
  const tempFiles = [
    path.join(process.cwd(), 'tsconfig.tsbuildinfo'),
  ];
  
  for (const file of tempFiles) {
    try {
      await fs.unlink(file);
      console.log(`✅ Cleared ${path.basename(file)}`);
    } catch (error) {
      // File doesn't exist, which is fine
    }
  }
}

async function clearLogFiles() {
  console.log('🗑️  Clearing log files...');
  
  const logDirs = [
    path.join(process.cwd(), 'logs'),
    path.join(process.cwd(), '.logs'),
  ];
  
  for (const dir of logDirs) {
    try {
      const files = await fs.readdir(dir);
      for (const file of files) {
        if (file.endsWith('.log')) {
          await fs.unlink(path.join(dir, file));
          console.log(`✅ Cleared log file: ${file}`);
        }
      }
    } catch (error) {
      // Directory doesn't exist, which is fine
    }
  }
}

async function optimizeDatabase() {
  console.log('🗑️  Optimizing database...');
  
  try {
    // Import Prisma client
    const { prisma } = await import('../lib/prisma');
    
    // Clear old logs (older than 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const deletedLogs = await prisma.userLog.deleteMany({
      where: {
        createdAt: {
          lt: thirtyDaysAgo,
        },
      },
    });
    
    console.log(`✅ Cleared ${deletedLogs.count} old log entries`);
    
    // Clear old login history (older than 90 days)
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
    
    const deletedHistory = await prisma.loginHistory.deleteMany({
      where: {
        createdAt: {
          lt: ninetyDaysAgo,
        },
      },
    });
    
    console.log(`✅ Cleared ${deletedHistory.count} old login history entries`);
    
    // Clear old workflow executions (older than 60 days)
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);
    
    const deletedExecutions = await prisma.workflowExecution.deleteMany({
      where: {
        createdAt: {
          lt: sixtyDaysAgo,
        },
      },
    });
    
    console.log(`✅ Cleared ${deletedExecutions.count} old workflow executions`);
    
    await prisma.$disconnect();
    
  } catch (error) {
    console.error('❌ Error optimizing database:', error);
  }
}

async function showCacheStats() {
  console.log('\n📊 Cache Statistics:');
  
  const dirs = [
    { name: 'Next.js cache', path: '.next/cache' },
    { name: 'Node modules cache', path: 'node_modules/.cache' },
    { name: 'Build output', path: '.next' },
  ];
  
  for (const { name, path: dirPath } of dirs) {
    try {
      const fullPath = path.join(process.cwd(), dirPath);
      const stats = await fs.stat(fullPath);
      
      if (stats.isDirectory()) {
        const files = await fs.readdir(fullPath, { recursive: true });
        console.log(`${name}: ${files.length} files`);
      }
    } catch (error) {
      console.log(`${name}: Not found`);
    }
  }
}

async function main() {
  console.log('🧹 Starting cache cleanup...\n');
  
  const startTime = Date.now();
  
  // Show initial stats
  await showCacheStats();
  
  console.log('\n🚀 Clearing caches...');
  
  // Clear various caches
  await clearNextCache();
  await clearNodeModulesCache();
  await clearTurboCache();
  await clearBuildCache();
  await clearTempFiles();
  await clearLogFiles();
  
  // Optimize database
  await optimizeDatabase();
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  console.log('\n✨ Cache cleanup complete!');
  console.log(`⏱️  Total time: ${duration}ms`);
  
  console.log('\n💡 Recommendations:');
  console.log('1. Run this script regularly to maintain performance');
  console.log('2. Consider setting up automated cleanup jobs');
  console.log('3. Monitor disk usage and cache growth');
  console.log('4. Clear browser cache for testing');
  
  console.log('\n🔄 Next steps:');
  console.log('1. npm run build - Rebuild the application');
  console.log('2. npm run dev - Start development server');
  console.log('3. npm run perf:analyze - Analyze performance');
}

if (require.main === module) {
  main().catch(console.error);
}

export { main as clearCache };
